# 在线客服系统测试文件说明

本目录包含了完整的HTML测试文件，用于测试在线客服系统的API接口和WebSocket功能。

## 📁 测试文件列表

### 1. `index.html` - 综合测试平台
**功能**: 集成了API测试和WebSocket测试的完整平台
**特点**:
- 🎨 现代化UI设计，响应式布局
- 📡 支持所有HTTP方法的API测试
- 💬 完整的WebSocket连接和消息测试
- 🔧 可自定义请求参数和头部
- 📊 实时显示连接状态和消息历史

### 2. `chat-test.html` - 聊天功能专用测试
**功能**: 专门用于测试访客和客服之间的实时聊天
**特点**:
- 👤 访客端和客服端双窗口设计
- 💬 实时消息收发测试
- ⚡ WebSocket连接状态监控
- 🎯 模拟真实聊天场景
- 📱 移动端适配

### 3. `api-test.html` - API接口专用测试
**功能**: 专门用于测试REST API接口
**特点**:
- 🚀 快速测试预设接口
- ⚙️ 自定义API测试
- 📊 批量测试功能
- 📈 详细的响应分析
- 🔍 错误诊断和调试

## 🚀 使用方法

### 前提条件
1. 确保客服系统服务器已启动
   ```bash
   cd AiPro/Kefu-Server
   go run main.go
   ```
2. 服务器运行在以下端口：
   - HTTP服务: `http://localhost:8080`
   - WebSocket服务: `ws://localhost:8081`

### 测试步骤

#### 方法一：使用浏览器直接打开
```bash
# 在浏览器中打开任意测试文件
open test/index.html
# 或
open test/chat-test.html
# 或
open test/api-test.html
```

#### 方法二：使用本地HTTP服务器
```bash
# 在test目录下启动简单HTTP服务器
cd test
python3 -m http.server 8000

# 然后在浏览器访问
# http://localhost:8000/index.html
# http://localhost:8000/chat-test.html
# http://localhost:8000/api-test.html
```

## 🧪 测试用例

### API接口测试用例

#### 1. 前端接口测试
- **获取站点配置**: `GET /api/frontend/get-site-config?site_key=test123`
- **初始化聊天**: `POST /api/frontend/init-chat`
- **发送消息**: `POST /api/frontend/send-message`
- **获取聊天历史**: `GET /api/frontend/get-chat-history`

#### 2. 后端接口测试
- **公司管理**: 
  - `GET /api/backend/get-company-list`
  - `POST /api/backend/create-company`
  - `PUT /api/backend/update-company`
  - `DELETE /api/backend/delete-company/:id`
- **座席管理**:
  - `GET /api/backend/get-seat-list`
  - `POST /api/backend/create-seat`
  - `POST /api/backend/renew-seat`
- **站点管理**:
  - `GET /api/backend/get-site-list`
  - `POST /api/backend/create-site`
  - `PUT /api/backend/update-site`

### WebSocket测试用例

#### 1. 连接测试
- 访客连接: `ws://localhost:8081/ws?type=visitor&user_id=visitor_001&site_id=1&session_id=session_001`
- 客服连接: `ws://localhost:8081/ws?type=customer_service&user_id=service_001&site_id=1&session_id=session_001`

#### 2. 消息类型测试
- **聊天消息**: `type: "chat_message"`
- **正在输入**: `type: "typing"`
- **消息已读**: `type: "read_message"`
- **系统消息**: `type: "system"`

## 📋 测试检查清单

### ✅ API接口测试
- [ ] 服务器启动正常
- [ ] 前端站点配置接口返回正确数据
- [ ] 后端管理接口响应正常
- [ ] 参数验证工作正常
- [ ] 错误处理返回适当的错误信息
- [ ] CORS跨域请求正常

### ✅ WebSocket测试
- [ ] WebSocket服务器启动正常
- [ ] 访客端连接成功
- [ ] 客服端连接成功
- [ ] 消息双向传输正常
- [ ] 连接断开处理正常
- [ ] 多种消息类型支持

### ✅ 性能测试
- [ ] API响应时间合理（< 1秒）
- [ ] WebSocket连接稳定
- [ ] 并发连接处理正常
- [ ] 内存使用正常

## 🐛 常见问题排查

### 1. API请求失败
**问题**: 请求返回404或500错误
**解决**:
- 检查服务器是否启动
- 确认API路径是否正确
- 查看服务器日志

### 2. WebSocket连接失败
**问题**: WebSocket无法连接
**解决**:
- 检查WebSocket服务器是否启动在8081端口
- 确认浏览器支持WebSocket
- 检查防火墙设置

### 3. CORS跨域问题
**问题**: 浏览器报CORS错误
**解决**:
- 确认服务器已启用CORS中间件
- 使用本地HTTP服务器提供测试页面

### 4. 参数格式错误
**问题**: API返回参数错误
**解决**:
- 检查JSON格式是否正确
- 确认必需参数是否提供
- 查看API文档确认参数要求

## 📊 测试报告

测试完成后，建议记录以下信息：
- 测试时间和环境
- 成功的测试用例
- 失败的测试用例和错误信息
- 性能数据（响应时间、并发数等）
- 发现的问题和建议

## 🔧 自定义测试

### 添加新的测试用例
1. 在`api-test.html`中的`testCases`对象添加新用例
2. 在`chat-test.html`中添加新的消息类型测试
3. 根据需要修改UI和交互逻辑

### 扩展测试功能
- 添加性能测试功能
- 集成自动化测试脚本
- 添加测试报告生成功能
- 支持测试数据导入导出

## 📞 技术支持

如果在测试过程中遇到问题，请：
1. 查看浏览器开发者工具的控制台错误
2. 检查服务器日志输出
3. 确认网络连接和端口访问
4. 参考项目文档和API说明

---

**注意**: 这些测试文件仅用于开发和测试环境，不应在生产环境中使用。
