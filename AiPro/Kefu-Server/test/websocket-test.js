// WebSocket测试脚本 (Node.js)
// 使用方法: node websocket-test.js

const WebSocket = require('ws');

const WS_URL = 'ws://localhost:8081/ws';
const SESSION_ID = 'test_session_' + Date.now();

let visitorWs = null;
let serviceWs = null;

console.log('🚀 开始WebSocket测试...');
console.log('会话ID:', SESSION_ID);

// 创建访客连接
function createVisitorConnection() {
    return new Promise((resolve, reject) => {
        const url = `${WS_URL}?type=visitor&user_id=visitor_001&site_id=1&session_id=${SESSION_ID}`;
        console.log('\n👤 创建访客连接:', url);
        
        visitorWs = new WebSocket(url);
        
        visitorWs.on('open', () => {
            console.log('✅ 访客连接成功');
            resolve();
        });
        
        visitorWs.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log('👤 访客收到消息:', message);
            } catch (e) {
                console.log('👤 访客收到原始消息:', data.toString());
            }
        });
        
        visitorWs.on('error', (error) => {
            console.error('❌ 访客连接错误:', error);
            reject(error);
        });
        
        visitorWs.on('close', () => {
            console.log('👤 访客连接关闭');
        });
    });
}

// 创建客服连接
function createServiceConnection() {
    return new Promise((resolve, reject) => {
        const url = `${WS_URL}?type=customer_service&user_id=service_001&site_id=1&session_id=${SESSION_ID}`;
        console.log('\n🎧 创建客服连接:', url);
        
        serviceWs = new WebSocket(url);
        
        serviceWs.on('open', () => {
            console.log('✅ 客服连接成功');
            resolve();
        });
        
        serviceWs.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log('🎧 客服收到消息:', message);
            } catch (e) {
                console.log('🎧 客服收到原始消息:', data.toString());
            }
        });
        
        serviceWs.on('error', (error) => {
            console.error('❌ 客服连接错误:', error);
            reject(error);
        });
        
        serviceWs.on('close', () => {
            console.log('🎧 客服连接关闭');
        });
    });
}

// 发送测试消息
function sendTestMessages() {
    console.log('\n📤 开始发送测试消息...');
    
    // 访客发送消息给客服
    setTimeout(() => {
        const visitorMessage = {
            type: 'chat_message',
            from: 'visitor_001',
            to: 'service_001',
            session_id: SESSION_ID,
            data: {
                content: '你好，我需要帮助',
                message_type: 1
            },
            timestamp: Date.now()
        };
        
        console.log('👤 访客发送消息:', visitorMessage);
        visitorWs.send(JSON.stringify(visitorMessage));
    }, 1000);
    
    // 客服回复消息
    setTimeout(() => {
        const serviceMessage = {
            type: 'chat_message',
            from: 'service_001',
            to: 'visitor_001',
            session_id: SESSION_ID,
            data: {
                content: '您好！我是客服，很高兴为您服务',
                message_type: 1
            },
            timestamp: Date.now()
        };
        
        console.log('🎧 客服发送消息:', serviceMessage);
        serviceWs.send(JSON.stringify(serviceMessage));
    }, 3000);
    
    // 测试输入状态
    setTimeout(() => {
        const typingMessage = {
            type: 'typing',
            from: 'visitor_001',
            to: 'service_001',
            session_id: SESSION_ID,
            data: {
                typing: true
            },
            timestamp: Date.now()
        };
        
        console.log('👤 访客发送输入状态:', typingMessage);
        visitorWs.send(JSON.stringify(typingMessage));
    }, 5000);
    
    // 关闭连接
    setTimeout(() => {
        console.log('\n🔚 测试完成，关闭连接...');
        if (visitorWs) visitorWs.close();
        if (serviceWs) serviceWs.close();
        
        setTimeout(() => {
            console.log('✅ WebSocket测试完成');
            process.exit(0);
        }, 1000);
    }, 7000);
}

// 主测试流程
async function runTest() {
    try {
        await createVisitorConnection();
        await createServiceConnection();
        
        // 等待连接稳定
        setTimeout(sendTestMessages, 500);
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    }
}

// 启动测试
runTest();
