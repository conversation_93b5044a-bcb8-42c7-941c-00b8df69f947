<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线客服系统 - API & WebSocket 测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
        
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .response.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .response.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        
        .chat-messages {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            padding: 10px;
            margin: 15px 0;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.sent {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message.received {
            background: #e9ecef;
            color: #495057;
        }
        
        .message.system {
            background: #fff3cd;
            color: #856404;
            text-align: center;
            max-width: 100%;
            font-style: italic;
        }
        
        .timestamp {
            font-size: 10px;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 在线客服系统测试平台</h1>
            <p>API接口测试 & WebSocket实时通讯测试</p>
        </div>
        
        <div class="content">
            <!-- API测试区域 -->
            <div class="section">
                <h2>📡 API接口测试</h2>
                
                <div class="form-group">
                    <label>服务器地址:</label>
                    <input type="text" id="apiBaseUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
                </div>
                
                <div class="form-group">
                    <label>测试接口:</label>
                    <select id="apiEndpoint">
                        <option value="/api/frontend/get-site-config">前端-获取站点配置</option>
                        <option value="/api/backend/get-company-list">后端-获取公司列表</option>
                        <option value="/api/backend/get-seat-list">后端-获取座席列表</option>
                        <option value="/api/backend/get-site-list">后端-获取站点列表</option>
                        <option value="/api/frontend/init-chat">前端-初始化聊天</option>
                        <option value="/api/frontend/send-message">前端-发送消息</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>请求参数 (JSON格式):</label>
                    <textarea id="apiParams" rows="4" placeholder='{"site_key": "test123", "page": 1, "limit": 10}'></textarea>
                </div>
                
                <div class="form-group">
                    <label>请求方法:</label>
                    <select id="apiMethod">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                    </select>
                </div>
                
                <button onclick="testAPI()">🚀 发送请求</button>
                <button onclick="clearAPIResponse()" class="btn-warning">🧹 清空响应</button>
                
                <div id="apiResponse" class="response"></div>
            </div>
            
            <!-- WebSocket测试区域 -->
            <div class="section">
                <h2>💬 WebSocket测试</h2>
                
                <div class="form-group">
                    <label>WebSocket地址:</label>
                    <input type="text" id="wsUrl" value="ws://localhost:8081/ws" placeholder="ws://localhost:8081/ws">
                </div>
                
                <div class="form-group">
                    <label>连接类型:</label>
                    <select id="wsType">
                        <option value="visitor">访客</option>
                        <option value="customer_service">客服</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="wsUserId" value="test_user_001" placeholder="用户唯一标识">
                </div>
                
                <div class="form-group">
                    <label>站点ID:</label>
                    <input type="number" id="wsSiteId" value="1" placeholder="站点ID">
                </div>
                
                <div class="form-group">
                    <label>会话ID:</label>
                    <input type="text" id="wsSessionId" value="session_001" placeholder="会话ID">
                </div>
                
                <div>
                    <span id="wsStatus" class="status disconnected">未连接</span>
                </div>
                
                <button onclick="connectWebSocket()" class="btn-success">🔗 连接</button>
                <button onclick="disconnectWebSocket()" class="btn-danger">❌ 断开</button>
                <button onclick="clearMessages()" class="btn-warning">🧹 清空消息</button>
                
                <div id="chatMessages" class="chat-messages"></div>
                
                <div class="form-group">
                    <label>发送消息:</label>
                    <textarea id="messageInput" rows="3" placeholder="输入要发送的消息..."></textarea>
                </div>
                
                <div class="form-group">
                    <label>消息类型:</label>
                    <select id="messageType">
                        <option value="chat_message">聊天消息</option>
                        <option value="typing">正在输入</option>
                        <option value="read_message">消息已读</option>
                        <option value="system">系统消息</option>
                    </select>
                </div>
                
                <button onclick="sendMessage()" class="btn-success">📤 发送消息</button>
                <button onclick="sendTyping()" class="btn-warning">✍️ 发送输入状态</button>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let wsConnected = false;

        // API测试功能
        async function testAPI() {
            const baseUrl = document.getElementById('apiBaseUrl').value;
            const endpoint = document.getElementById('apiEndpoint').value;
            const method = document.getElementById('apiMethod').value;
            const paramsText = document.getElementById('apiParams').value;
            
            let url = baseUrl + endpoint;
            let options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            try {
                // 处理参数
                if (paramsText.trim()) {
                    const params = JSON.parse(paramsText);
                    
                    if (method === 'GET') {
                        // GET请求将参数添加到URL
                        const urlParams = new URLSearchParams(params);
                        url += '?' + urlParams.toString();
                    } else {
                        // POST/PUT/DELETE请求将参数放在body中
                        options.body = JSON.stringify(params);
                    }
                }
                
                updateAPIResponse('发送请求中...', 'info');
                
                const response = await fetch(url, options);
                const data = await response.json();
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    url: url,
                    method: method,
                    data: data
                };
                
                updateAPIResponse(JSON.stringify(result, null, 2), response.ok ? 'success' : 'error');
                
            } catch (error) {
                updateAPIResponse('请求失败: ' + error.message, 'error');
            }
        }
        
        function updateAPIResponse(text, type = 'info') {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.textContent = text;
            responseDiv.className = 'response ' + type;
        }
        
        function clearAPIResponse() {
            document.getElementById('apiResponse').textContent = '';
            document.getElementById('apiResponse').className = 'response';
        }
        
        // 预设API参数
        document.getElementById('apiEndpoint').addEventListener('change', function() {
            const endpoint = this.value;
            const paramsInput = document.getElementById('apiParams');
            const methodSelect = document.getElementById('apiMethod');
            
            switch(endpoint) {
                case '/api/frontend/get-site-config':
                    paramsInput.value = '{"site_key": "test123"}';
                    methodSelect.value = 'GET';
                    break;
                case '/api/backend/get-company-list':
                    paramsInput.value = '{"page": 1, "limit": 10, "keyword": ""}';
                    methodSelect.value = 'GET';
                    break;
                case '/api/backend/get-seat-list':
                    paramsInput.value = '{"company_id": 1, "page": 1, "limit": 10}';
                    methodSelect.value = 'GET';
                    break;
                case '/api/backend/get-site-list':
                    paramsInput.value = '{"company_id": 1, "page": 1, "limit": 10}';
                    methodSelect.value = 'GET';
                    break;
                case '/api/frontend/init-chat':
                    paramsInput.value = JSON.stringify({
                        "site_key": "test123",
                        "visitor_id": "visitor_001",
                        "nickname": "测试访客",
                        "email": "<EMAIL>",
                        "page_url": "https://example.com",
                        "page_title": "测试页面"
                    }, null, 2);
                    methodSelect.value = 'POST';
                    break;
                case '/api/frontend/send-message':
                    paramsInput.value = JSON.stringify({
                        "session_id": "session_001",
                        "message_type": 1,
                        "content": "你好，我需要帮助"
                    }, null, 2);
                    methodSelect.value = 'POST';
                    break;
            }
        });
        
        // WebSocket功能
        function connectWebSocket() {
            if (wsConnected) {
                addMessage('系统', '已经连接，请先断开', 'system');
                return;
            }
            
            const url = document.getElementById('wsUrl').value;
            const type = document.getElementById('wsType').value;
            const userId = document.getElementById('wsUserId').value;
            const siteId = document.getElementById('wsSiteId').value;
            const sessionId = document.getElementById('wsSessionId').value;
            
            const wsUrlWithParams = `${url}?type=${type}&user_id=${userId}&site_id=${siteId}&session_id=${sessionId}`;
            
            updateWSStatus('connecting', '连接中...');
            addMessage('系统', `正在连接到: ${wsUrlWithParams}`, 'system');
            
            try {
                ws = new WebSocket(wsUrlWithParams);
                
                ws.onopen = function(event) {
                    wsConnected = true;
                    updateWSStatus('connected', '已连接');
                    addMessage('系统', 'WebSocket连接成功！', 'system');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        addMessage('收到', JSON.stringify(message, null, 2), 'received');
                    } catch (e) {
                        addMessage('收到', event.data, 'received');
                    }
                };
                
                ws.onclose = function(event) {
                    wsConnected = false;
                    updateWSStatus('disconnected', '已断开');
                    addMessage('系统', `连接关闭: ${event.code} - ${event.reason}`, 'system');
                };
                
                ws.onerror = function(error) {
                    wsConnected = false;
                    updateWSStatus('disconnected', '连接错误');
                    addMessage('系统', '连接错误: ' + error, 'system');
                };
                
            } catch (error) {
                updateWSStatus('disconnected', '连接失败');
                addMessage('系统', '连接失败: ' + error.message, 'system');
            }
        }
        
        function disconnectWebSocket() {
            if (ws && wsConnected) {
                ws.close();
                addMessage('系统', '主动断开连接', 'system');
            }
        }
        
        function sendMessage() {
            if (!wsConnected) {
                addMessage('系统', '请先连接WebSocket', 'system');
                return;
            }
            
            const content = document.getElementById('messageInput').value;
            const messageType = document.getElementById('messageType').value;
            
            if (!content.trim()) {
                addMessage('系统', '请输入消息内容', 'system');
                return;
            }
            
            const message = {
                type: messageType,
                from: document.getElementById('wsUserId').value,
                to: messageType === 'chat_message' ? 'customer_service_001' : '',
                session_id: document.getElementById('wsSessionId').value,
                data: {
                    content: content,
                    message_type: 1
                },
                timestamp: Date.now()
            };
            
            try {
                ws.send(JSON.stringify(message));
                addMessage('发送', JSON.stringify(message, null, 2), 'sent');
                document.getElementById('messageInput').value = '';
            } catch (error) {
                addMessage('系统', '发送失败: ' + error.message, 'system');
            }
        }
        
        function sendTyping() {
            if (!wsConnected) {
                addMessage('系统', '请先连接WebSocket', 'system');
                return;
            }
            
            const message = {
                type: 'typing',
                from: document.getElementById('wsUserId').value,
                to: 'customer_service_001',
                session_id: document.getElementById('wsSessionId').value,
                data: {
                    typing: true
                },
                timestamp: Date.now()
            };
            
            try {
                ws.send(JSON.stringify(message));
                addMessage('发送', '正在输入状态', 'sent');
            } catch (error) {
                addMessage('系统', '发送失败: ' + error.message, 'system');
            }
        }
        
        function updateWSStatus(status, text) {
            const statusElement = document.getElementById('wsStatus');
            statusElement.className = 'status ' + status;
            statusElement.textContent = text;
        }
        
        function addMessage(sender, content, type) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <strong>${sender}:</strong><br>
                ${content}
                <div class="timestamp">${timestamp}</div>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function clearMessages() {
            document.getElementById('chatMessages').innerHTML = '';
        }
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 触发一次endpoint change事件来设置默认参数
            document.getElementById('apiEndpoint').dispatchEvent(new Event('change'));
            
            addMessage('系统', '测试平台已就绪，请选择要测试的功能', 'system');
        });
    </script>
</body>
</html>
