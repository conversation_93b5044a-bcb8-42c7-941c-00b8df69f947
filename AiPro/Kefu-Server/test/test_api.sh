#!/bin/bash

# 在线客服系统API测试脚本

BASE_URL="http://localhost:8080"

echo "=== 在线客服系统API测试 ==="
echo "基础URL: $BASE_URL"
echo ""

# 测试前端接口
echo "1. 测试前端站点配置接口"
echo "GET $BASE_URL/api/frontend/site/config?site_key=test123"
curl -s -X GET "$BASE_URL/api/frontend/site/config?site_key=test123" | jq . || echo "响应: $(curl -s -X GET "$BASE_URL/api/frontend/site/config?site_key=test123")"
echo ""

# 测试后端接口
echo "2. 测试后端公司列表接口"
echo "GET $BASE_URL/api/backend/company/list?page=1&limit=10"
curl -s -X GET "$BASE_URL/api/backend/company/list?page=1&limit=10" | jq . || echo "响应: $(curl -s -X GET "$BASE_URL/api/backend/company/list?page=1&limit=10")"
echo ""

echo "3. 测试后端座席列表接口"
echo "GET $BASE_URL/api/backend/seat/list?company_id=1&page=1&limit=10"
curl -s -X GET "$BASE_URL/api/backend/seat/list?company_id=1&page=1&limit=10" | jq . || echo "响应: $(curl -s -X GET "$BASE_URL/api/backend/seat/list?company_id=1&page=1&limit=10")"
echo ""

echo "4. 测试WebSocket连接"
echo "WebSocket地址: ws://localhost:8081/ws?type=visitor&user_id=test123&site_id=1"
echo "注意: WebSocket需要使用专门的客户端工具测试"
echo ""

echo "=== 测试完成 ==="
echo "如果看到JSON响应，说明API接口正常工作"
echo "如果看到连接错误，请检查服务是否正常启动"
