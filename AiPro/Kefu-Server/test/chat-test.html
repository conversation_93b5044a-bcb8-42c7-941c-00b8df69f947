<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服聊天测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
        }

        .chat-container {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        .visitor-panel, .service-panel {
            flex: 1;
            background: white;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }

        .visitor-panel .panel-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .service-panel .panel-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .panel-header h3 {
            margin-bottom: 10px;
        }

        .status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            background: rgba(255,255,255,0.2);
            display: inline-block;
        }

        .status.connected {
            background: rgba(76, 175, 80, 0.8);
        }

        .status.disconnected {
            background: rgba(244, 67, 54, 0.8);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
        }

        .message.sent {
            margin-left: auto;
        }

        .message.received {
            margin-right: auto;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            position: relative;
        }

        .message.sent .message-bubble {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.received .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .message.system .message-bubble {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            text-align: center;
            border-radius: 12px;
            font-size: 12px;
            max-width: 100%;
            margin: 0 auto;
        }

        .message-time {
            font-size: 10px;
            color: #999;
            margin-top: 5px;
            text-align: right;
        }

        .message.received .message-time {
            text-align: left;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #eee;
            background: white;
            border-radius: 0 0 10px 10px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }

        .input-group input:focus {
            border-color: #007bff;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .typing-indicator {
            font-style: italic;
            color: #666;
            font-size: 12px;
            padding: 5px 15px;
        }

        @media (max-width: 768px) {
            .chat-container {
                flex-direction: column;
            }

            .visitor-panel, .service-panel {
                height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 访客端 -->
        <div class="visitor-panel">
            <div class="panel-header">
                <h3>👤 访客端</h3>
                <span id="visitorStatus" class="status disconnected">未连接</span>
            </div>

            <div id="visitorMessages" class="chat-messages"></div>

            <div class="chat-input">
                <div class="controls">
                    <button onclick="connectVisitor()" class="btn btn-success">连接</button>
                    <button onclick="disconnectVisitor()" class="btn btn-danger">断开</button>
                    <button onclick="clearVisitorMessages()" class="btn">清空</button>
                </div>
                <div class="input-group">
                    <input type="text" id="visitorInput" placeholder="输入消息..." onkeypress="handleVisitorKeyPress(event)">
                    <button onclick="sendVisitorMessage()" class="btn btn-primary">发送</button>
                </div>
            </div>
        </div>

        <!-- 客服端 -->
        <div class="service-panel">
            <div class="panel-header">
                <h3>🎧 客服端</h3>
                <span id="serviceStatus" class="status disconnected">未连接</span>
            </div>

            <div id="serviceMessages" class="chat-messages"></div>

            <div class="chat-input">
                <div class="controls">
                    <button onclick="connectService()" class="btn btn-success">连接</button>
                    <button onclick="disconnectService()" class="btn btn-danger">断开</button>
                    <button onclick="clearServiceMessages()" class="btn">清空</button>
                </div>
                <div class="input-group">
                    <input type="text" id="serviceInput" placeholder="输入回复..." onkeypress="handleServiceKeyPress(event)">
                    <button onclick="sendServiceMessage()" class="btn btn-primary">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let visitorWs = null;
        let serviceWs = null;
        let visitorConnected = false;
        let serviceConnected = false;

        const WS_URL = 'ws://localhost:8081/ws';
        const SESSION_ID = 'test_session_' + Date.now();

        // 访客端连接
        function connectVisitor() {
            if (visitorConnected) return;

            const url = `${WS_URL}?type=visitor&user_id=visitor_001&site_id=1&session_id=${SESSION_ID}`;

            visitorWs = new WebSocket(url);

            visitorWs.onopen = function() {
                visitorConnected = true;
                updateVisitorStatus('connected', '已连接');
                addVisitorMessage('系统', '访客连接成功', 'system');
            };

            visitorWs.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    if (message.type === 'chat_message') {
                        addVisitorMessage('客服', message.data.content, 'received');
                    } else if (message.type === 'typing') {
                        showTypingIndicator('visitor', '客服正在输入...');
                    }
                } catch (e) {
                    addVisitorMessage('系统', '收到消息: ' + event.data, 'system');
                }
            };

            visitorWs.onclose = function() {
                visitorConnected = false;
                updateVisitorStatus('disconnected', '已断开');
                addVisitorMessage('系统', '连接已断开', 'system');
            };

            visitorWs.onerror = function(error) {
                addVisitorMessage('系统', '连接错误', 'system');
            };
        }

        // 客服端连接
        function connectService() {
            if (serviceConnected) return;

            const url = `${WS_URL}?type=customer_service&user_id=service_001&site_id=1&session_id=${SESSION_ID}`;

            serviceWs = new WebSocket(url);

            serviceWs.onopen = function() {
                serviceConnected = true;
                updateServiceStatus('connected', '已连接');
                addServiceMessage('系统', '客服连接成功', 'system');
            };

            serviceWs.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    if (message.type === 'chat_message') {
                        addServiceMessage('访客', message.data.content, 'received');
                    } else if (message.type === 'typing') {
                        showTypingIndicator('service', '访客正在输入...');
                    }
                } catch (e) {
                    addServiceMessage('系统', '收到消息: ' + event.data, 'system');
                }
            };

            serviceWs.onclose = function() {
                serviceConnected = false;
                updateServiceStatus('disconnected', '已断开');
                addServiceMessage('系统', '连接已断开', 'system');
            };

            serviceWs.onerror = function(error) {
                addServiceMessage('系统', '连接错误', 'system');
            };
        }

        // 断开连接
        function disconnectVisitor() {
            if (visitorWs && visitorConnected) {
                visitorWs.close();
            }
        }

        function disconnectService() {
            if (serviceWs && serviceConnected) {
                serviceWs.close();
            }
        }

        // 发送消息
        function sendVisitorMessage() {
            const input = document.getElementById('visitorInput');
            const content = input.value.trim();

            if (!content || !visitorConnected) return;

            const message = {
                type: 'chat_message',
                from: 'visitor_001',
                to: 'service_001',
                session_id: SESSION_ID,
                data: {
                    content: content,
                    message_type: 1
                },
                timestamp: Date.now()
            };

            console.log('访客发送消息:', message);
            visitorWs.send(JSON.stringify(message));
            addVisitorMessage('我', content, 'sent');
            input.value = '';
        }

        function sendServiceMessage() {
            const input = document.getElementById('serviceInput');
            const content = input.value.trim();

            if (!content || !serviceConnected) return;

            const message = {
                type: 'chat_message',
                from: 'service_001',
                to: 'visitor_001',
                session_id: SESSION_ID,
                data: {
                    content: content,
                    message_type: 1
                },
                timestamp: Date.now()
            };

            console.log('客服发送消息:', message);
            serviceWs.send(JSON.stringify(message));
            addServiceMessage('我', content, 'sent');
            input.value = '';
        }

        // 键盘事件
        function handleVisitorKeyPress(event) {
            if (event.key === 'Enter') {
                sendVisitorMessage();
            }
        }

        function handleServiceKeyPress(event) {
            if (event.key === 'Enter') {
                sendServiceMessage();
            }
        }

        // 更新状态
        function updateVisitorStatus(status, text) {
            const statusElement = document.getElementById('visitorStatus');
            statusElement.className = 'status ' + status;
            statusElement.textContent = text;
        }

        function updateServiceStatus(status, text) {
            const statusElement = document.getElementById('serviceStatus');
            statusElement.className = 'status ' + status;
            statusElement.textContent = text;
        }

        // 添加消息
        function addVisitorMessage(sender, content, type) {
            addMessage('visitorMessages', sender, content, type);
        }

        function addServiceMessage(sender, content, type) {
            addMessage('serviceMessages', sender, content, type);
        }

        function addMessage(containerId, sender, content, type) {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;

            const time = new Date().toLocaleTimeString();

            messageDiv.innerHTML = `
                <div class="message-bubble">
                    ${type !== 'system' ? '<strong>' + sender + ':</strong><br>' : ''}
                    ${content}
                </div>
                <div class="message-time">${time}</div>
            `;

            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 清空消息
        function clearVisitorMessages() {
            document.getElementById('visitorMessages').innerHTML = '';
        }

        function clearServiceMessages() {
            document.getElementById('serviceMessages').innerHTML = '';
        }

        // 显示输入状态
        function showTypingIndicator(panel, text) {
            const containerId = panel === 'visitor' ? 'visitorMessages' : 'serviceMessages';
            const container = document.getElementById(containerId);

            // 移除之前的输入提示
            const existingIndicator = container.querySelector('.typing-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            // 添加新的输入提示
            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.textContent = text;
            container.appendChild(indicator);
            container.scrollTop = container.scrollHeight;

            // 3秒后自动移除
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.remove();
                }
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addVisitorMessage('系统', '欢迎使用客服聊天测试！点击"连接"开始测试', 'system');
            addServiceMessage('系统', '客服端已就绪，点击"连接"开始接待访客', 'system');
        });
    </script>
</body>
</html>
