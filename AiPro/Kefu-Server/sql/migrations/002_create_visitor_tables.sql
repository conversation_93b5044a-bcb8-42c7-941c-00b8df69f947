-- 访客表
CREATE TABLE IF NOT EXISTS `ks_visitor` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '访客ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `nickname` varchar(100) DEFAULT NULL COMMENT '访客昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '访客头像',
  `email` varchar(100) DEFAULT NULL COMMENT '访客邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '访客电话',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `referrer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `location` varchar(100) DEFAULT NULL COMMENT '地理位置',
  `first_visit` datetime DEFAULT NULL COMMENT '首次访问时间',
  `last_visit` datetime DEFAULT NULL COMMENT '最后访问时间',
  `visit_count` int(11) NOT NULL DEFAULT '0' COMMENT '访问次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_site_visitor` (`site_id`, `visitor_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客表';

-- 在线访客表
CREATE TABLE IF NOT EXISTS `ks_online_visitor` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `current_page` varchar(500) DEFAULT NULL COMMENT '当前页面',
  `online_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上线时间',
  `last_activity` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  `seat_id` bigint(20) unsigned DEFAULT NULL COMMENT '分配的座席ID',
  `customer_service_id` bigint(20) unsigned DEFAULT NULL COMMENT '分配的客服ID',
  `chat_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '聊天状态：0-未开始，1-进行中，2-已结束',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_seat_id` (`seat_id`),
  KEY `idx_customer_service_id` (`customer_service_id`),
  KEY `idx_chat_status` (`chat_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='在线访客表';

-- 访客历史表
CREATE TABLE IF NOT EXISTS `ks_visitor_history` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '持续时间(秒)',
  `page_views` int(11) NOT NULL DEFAULT '0' COMMENT '页面浏览数',
  `message_count` int(11) NOT NULL DEFAULT '0' COMMENT '消息数量',
  `seat_id` bigint(20) unsigned DEFAULT NULL COMMENT '座席ID',
  `customer_service_id` bigint(20) unsigned DEFAULT NULL COMMENT '客服ID',
  `satisfaction` tinyint(1) DEFAULT NULL COMMENT '满意度评分：1-5分',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_seat_id` (`seat_id`),
  KEY `idx_customer_service_id` (`customer_service_id`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客历史表';

-- 访客浏览记录表
CREATE TABLE IF NOT EXISTS `ks_visitor_browse_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `site_id` bigint(20) unsigned NOT NULL COMMENT '站点ID',
  `visitor_id` varchar(64) NOT NULL COMMENT '访客唯一标识',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `page_url` varchar(500) NOT NULL COMMENT '页面URL',
  `page_title` varchar(255) DEFAULT NULL COMMENT '页面标题',
  `referrer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `stay_time` int(11) NOT NULL DEFAULT '0' COMMENT '停留时间(秒)',
  `visit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_visit_time` (`visit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访客浏览记录表';
