package cmd

import (
	"context"
	"kefu-server/internal/controller/backend"
	"kefu-server/internal/controller/frontend"
	"kefu-server/internal/websocket"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start kefu server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			// 暂时注释数据库初始化，先测试HTTP接口
			// database.Init(ctx)

			// 启动WebSocket服务
			go websocket.StartWebSocketServer(ctx)

			// 启动HTTP服务
			s := g.Server()

			// 设置CORS
			s.Use(ghttp.MiddlewareCORS)

			// 注册路由
			registerRoutes(s)

			g.Log().Info(ctx, "HTTP服务器启动，监听端口: :8080")
			s.Run()
			return nil
		},
	}
)

func registerRoutes(s *ghttp.Server) {
	// 前端接口分组
	frontendGroup := s.Group("/api/frontend")
	{
		frontendGroup.Bind(
			frontend.NewSiteController(),
			frontend.NewChatController(),
		)
	}

	// 后端接口分组
	backendGroup := s.Group("/api/backend")
	{
		backendGroup.Bind(
			backend.NewCompanyController(),
			backend.NewSeatController(),
			backend.NewSiteController(),
			backend.NewSettingsController(),
			backend.NewChatController(),
		)
	}
}
