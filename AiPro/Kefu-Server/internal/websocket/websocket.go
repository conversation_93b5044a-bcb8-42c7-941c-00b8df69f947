package websocket

import (
	"context"
	"encoding/json"
	"net/http"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gorilla/websocket"
)

var (
	upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true // 允许跨域
		},
	}

	// 连接管理器
	manager = &ConnectionManager{
		connections: make(map[string]*Connection),
		mutex:       sync.RWMutex{},
	}
)

// Connection WebSocket连接
type Connection struct {
	ID        string          `json:"id"`         // 连接ID
	Type      string          `json:"type"`       // 连接类型：visitor, customer_service
	UserID    string          `json:"user_id"`    // 用户ID
	SiteID    uint64          `json:"site_id"`    // 站点ID
	SessionID string          `json:"session_id"` // 会话ID
	Conn      *websocket.Conn `json:"-"`          // WebSocket连接
	Send      chan []byte     `json:"-"`          // 发送通道
	mutex     sync.Mutex      `json:"-"`          // 互斥锁
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	connections map[string]*Connection
	mutex       sync.RWMutex
}

// Message WebSocket消息
type Message struct {
	Type      string      `json:"type"`       // 消息类型
	From      string      `json:"from"`       // 发送者
	To        string      `json:"to"`         // 接收者
	SessionID string      `json:"session_id"` // 会话ID
	Data      interface{} `json:"data"`       // 消息数据
	Timestamp int64       `json:"timestamp"`  // 时间戳
}

// StartWebSocketServer 启动WebSocket服务器
func StartWebSocketServer(ctx context.Context) {
	server := g.Server("websocket")
	server.SetAddr(g.Cfg().MustGet(ctx, "websocket.port").String())

	server.BindHandler("/ws", handleWebSocket)

	g.Log().Info(ctx, "WebSocket服务器启动，监听端口:", g.Cfg().MustGet(ctx, "websocket.port").String())
	server.Run()
}

// handleWebSocket 处理WebSocket连接
func handleWebSocket(r *ghttp.Request) {
	conn, err := upgrader.Upgrade(r.Response.ResponseWriter, r.Request, nil)
	if err != nil {
		g.Log().Error(r.Context(), "WebSocket升级失败:", err)
		return
	}
	defer conn.Close()

	// 获取连接参数
	userType := r.Get("type").String() // visitor 或 customer_service
	userID := r.Get("user_id").String()
	siteID := r.Get("site_id").Uint64()
	sessionID := r.Get("session_id").String()

	if userType == "" || userID == "" {
		g.Log().Error(r.Context(), "WebSocket连接参数不完整")
		return
	}

	// 创建连接
	connectionID := generateConnectionID(userType, userID)
	connection := &Connection{
		ID:        connectionID,
		Type:      userType,
		UserID:    userID,
		SiteID:    siteID,
		SessionID: sessionID,
		Conn:      conn,
		Send:      make(chan []byte, 256),
	}

	g.Log().Info(context.Background(), "创建WebSocket连接", "connectionID", connectionID, "type", userType, "userID", userID, "siteID", siteID, "sessionID", sessionID)

	// 注册连接
	manager.Register(connection)
	defer manager.Unregister(connection)

	// 启动读写协程
	go connection.writePump()
	connection.readPump()
}

// Register 注册连接
func (cm *ConnectionManager) Register(conn *Connection) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.connections[conn.ID] = conn
	g.Log().Info(context.Background(), "WebSocket连接注册:", conn.ID, "类型:", conn.Type)
}

// Unregister 注销连接
func (cm *ConnectionManager) Unregister(conn *Connection) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if _, ok := cm.connections[conn.ID]; ok {
		delete(cm.connections, conn.ID)
		close(conn.Send)
		g.Log().Info(context.Background(), "WebSocket连接注销:", conn.ID)
	}
}

// GetConnection 获取连接
func (cm *ConnectionManager) GetConnection(id string) *Connection {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return cm.connections[id]
}

// GetConnectionsByType 根据类型获取连接
func (cm *ConnectionManager) GetConnectionsByType(connType string) []*Connection {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	var connections []*Connection
	for _, conn := range cm.connections {
		if conn.Type == connType {
			connections = append(connections, conn)
		}
	}
	return connections
}

// GetConnectionsBySiteID 根据站点ID获取连接
func (cm *ConnectionManager) GetConnectionsBySiteID(siteID uint64) []*Connection {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	var connections []*Connection
	for _, conn := range cm.connections {
		if conn.SiteID == siteID {
			connections = append(connections, conn)
		}
	}
	return connections
}

// SendToConnection 发送消息到指定连接
func (cm *ConnectionManager) SendToConnection(connectionID string, message []byte) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if conn, ok := cm.connections[connectionID]; ok {
		select {
		case conn.Send <- message:
		default:
			close(conn.Send)
			delete(cm.connections, connectionID)
		}
	}
}

// BroadcastToSite 向站点广播消息
func (cm *ConnectionManager) BroadcastToSite(siteID uint64, message []byte) {
	connections := cm.GetConnectionsBySiteID(siteID)
	for _, conn := range connections {
		select {
		case conn.Send <- message:
		default:
			cm.Unregister(conn)
		}
	}
}

// readPump 读取消息
func (c *Connection) readPump() {
	defer c.Conn.Close()

	for {
		_, messageBytes, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				g.Log().Error(context.Background(), "WebSocket读取错误:", err)
			}
			break
		}

		// 处理接收到的消息
		c.handleMessage(messageBytes)
	}
}

// writePump 发送消息
func (c *Connection) writePump() {
	defer c.Conn.Close()

	for {
		select {
		case message, ok := <-c.Send:
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			c.mutex.Lock()
			err := c.Conn.WriteMessage(websocket.TextMessage, message)
			c.mutex.Unlock()

			if err != nil {
				g.Log().Error(context.Background(), "WebSocket发送错误:", err)
				return
			}
		}
	}
}

// handleMessage 处理消息
func (c *Connection) handleMessage(messageBytes []byte) {
	var msg Message
	if err := json.Unmarshal(messageBytes, &msg); err != nil {
		g.Log().Error(context.Background(), "消息解析失败:", err)
		return
	}

	g.Log().Info(context.Background(), "收到消息", "from", msg.From, "to", msg.To, "type", msg.Type, "sessionID", msg.SessionID)

	// 根据消息类型处理
	switch msg.Type {
	case "chat_message":
		c.handleChatMessage(&msg)
	case "typing":
		c.handleTyping(&msg)
	case "read_message":
		c.handleReadMessage(&msg)
	default:
		g.Log().Warning(context.Background(), "未知消息类型:", msg.Type)
	}
}

// handleChatMessage 处理聊天消息
func (c *Connection) handleChatMessage(msg *Message) {
	// 保存消息到数据库
	// TODO: 调用聊天服务保存消息

	// 转发消息给相关连接
	if msg.To != "" {
		// 根据发送者类型确定接收者类型
		var toConnectionID string
		if c.Type == "visitor" {
			// 访客发送给客服
			toConnectionID = generateConnectionID("customer_service", msg.To)
		} else if c.Type == "customer_service" {
			// 客服发送给访客
			toConnectionID = generateConnectionID("visitor", msg.To)
		} else {
			// 兼容旧的方式
			toConnectionID = msg.To
		}

		g.Log().Info(context.Background(), "转发消息", "from", c.ID, "to", toConnectionID, "content", msg.Data)

		if toConn := manager.GetConnection(toConnectionID); toConn != nil {
			messageBytes, _ := json.Marshal(msg)
			manager.SendToConnection(toConnectionID, messageBytes)
			g.Log().Info(context.Background(), "消息转发成功", "to", toConnectionID)
		} else {
			g.Log().Warning(context.Background(), "目标连接不存在", "toConnectionID", toConnectionID)
			// 列出所有当前连接用于调试
			manager.mutex.RLock()
			var connectionIDs []string
			for id := range manager.connections {
				connectionIDs = append(connectionIDs, id)
			}
			manager.mutex.RUnlock()
			g.Log().Info(context.Background(), "当前所有连接", "connections", connectionIDs)
		}
	}
}

// handleTyping 处理正在输入状态
func (c *Connection) handleTyping(msg *Message) {
	// 转发输入状态给相关连接
	if msg.To != "" {
		toConnectionID := generateConnectionID("", msg.To)
		messageBytes, _ := json.Marshal(msg)
		manager.SendToConnection(toConnectionID, messageBytes)
	}
}

// handleReadMessage 处理消息已读
func (c *Connection) handleReadMessage(msg *Message) {
	// 更新消息已读状态
	// TODO: 调用聊天服务更新消息状态
}

// generateConnectionID 生成连接ID
func generateConnectionID(userType, userID string) string {
	if userType != "" {
		return userType + "_" + userID
	}
	return userID
}

// GetManager 获取连接管理器
func GetManager() *ConnectionManager {
	return manager
}
