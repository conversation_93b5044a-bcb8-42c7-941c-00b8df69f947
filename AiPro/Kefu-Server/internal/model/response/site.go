package response

import "kefu-server/internal/model/entity"

// SiteConfigResponse 站点配置响应结构
type SiteConfigResponse struct {
	Site                    *entity.Site                    `json:"site"`                      // 站点信息
	SiteSettings            *entity.SiteSettings            `json:"site_settings"`             // 站点设置
	InvitePopupSettings     *entity.InvitePopupSettings     `json:"invite_popup_settings"`     // 邀请弹窗设置
	ConsultIconSettings     *entity.ConsultIconSettings     `json:"consult_icon_settings"`     // 咨询图标设置
	QuickChatSettings       *entity.QuickChatSettings       `json:"quick_chat_settings"`       // 快捷对话设置
	IndependentChatSettings *entity.IndependentChatSettings `json:"independent_chat_settings"` // 独立对话设置
	VisitorMessageSettings  *entity.VisitorMessageSettings  `json:"visitor_message_settings"`  // 访客留言设置
	OnlineCustomerServices  []CustomerServiceInfo           `json:"online_customer_services"`  // 在线客服列表
}

// CustomerServiceInfo 客服信息
type CustomerServiceInfo struct {
	Id       uint64 `json:"id"`       // 客服ID
	Nickname string `json:"nickname"` // 客服昵称
	Avatar   string `json:"avatar"`   // 客服头像
	Status   int    `json:"status"`   // 客服状态
}
