package entity

import "github.com/gogf/gf/v2/os/gtime"

// Visitor 访客表
type Visitor struct {
	Id          uint64      `json:"id" orm:"id,primary"`             // 访客ID
	SiteId      uint64      `json:"site_id" orm:"site_id"`           // 站点ID
	VisitorId   string      `json:"visitor_id" orm:"visitor_id"`     // 访客唯一标识
	Nickname    string      `json:"nickname" orm:"nickname"`         // 访客昵称
	Avatar      string      `json:"avatar" orm:"avatar"`             // 访客头像
	Email       string      `json:"email" orm:"email"`               // 访客邮箱
	Phone       string      `json:"phone" orm:"phone"`               // 访客电话
	IpAddress   string      `json:"ip_address" orm:"ip_address"`     // IP地址
	UserAgent   string      `json:"user_agent" orm:"user_agent"`     // 用户代理
	Referrer    string      `json:"referrer" orm:"referrer"`         // 来源页面
	Location    string      `json:"location" orm:"location"`         // 地理位置
	FirstVisit  *gtime.Time `json:"first_visit" orm:"first_visit"`   // 首次访问时间
	LastVisit   *gtime.Time `json:"last_visit" orm:"last_visit"`     // 最后访问时间
	VisitCount  int         `json:"visit_count" orm:"visit_count"`   // 访问次数
	Status      int         `json:"status" orm:"status"`             // 状态：1-正常，0-禁用
	CreatedAt   *gtime.Time `json:"created_at" orm:"created_at"`     // 创建时间
	UpdatedAt   *gtime.Time `json:"updated_at" orm:"updated_at"`     // 更新时间
}

// OnlineVisitor 在线访客表
type OnlineVisitor struct {
	Id            uint64      `json:"id" orm:"id,primary"`                       // ID
	SiteId        uint64      `json:"site_id" orm:"site_id"`                     // 站点ID
	VisitorId     string      `json:"visitor_id" orm:"visitor_id"`               // 访客唯一标识
	SessionId     string      `json:"session_id" orm:"session_id"`               // 会话ID
	CurrentPage   string      `json:"current_page" orm:"current_page"`           // 当前页面
	OnlineTime    *gtime.Time `json:"online_time" orm:"online_time"`             // 上线时间
	LastActivity  *gtime.Time `json:"last_activity" orm:"last_activity"`         // 最后活动时间
	SeatId        uint64      `json:"seat_id" orm:"seat_id"`                     // 分配的座席ID
	CustomerServiceId uint64  `json:"customer_service_id" orm:"customer_service_id"` // 分配的客服ID
	ChatStatus    int         `json:"chat_status" orm:"chat_status"`             // 聊天状态：0-未开始，1-进行中，2-已结束
	CreatedAt     *gtime.Time `json:"created_at" orm:"created_at"`               // 创建时间
	UpdatedAt     *gtime.Time `json:"updated_at" orm:"updated_at"`               // 更新时间
}

// VisitorHistory 访客历史表
type VisitorHistory struct {
	Id          uint64      `json:"id" orm:"id,primary"`             // ID
	SiteId      uint64      `json:"site_id" orm:"site_id"`           // 站点ID
	VisitorId   string      `json:"visitor_id" orm:"visitor_id"`     // 访客唯一标识
	SessionId   string      `json:"session_id" orm:"session_id"`     // 会话ID
	StartTime   *gtime.Time `json:"start_time" orm:"start_time"`     // 开始时间
	EndTime     *gtime.Time `json:"end_time" orm:"end_time"`         // 结束时间
	Duration    int         `json:"duration" orm:"duration"`         // 持续时间(秒)
	PageViews   int         `json:"page_views" orm:"page_views"`     // 页面浏览数
	MessageCount int        `json:"message_count" orm:"message_count"` // 消息数量
	SeatId      uint64      `json:"seat_id" orm:"seat_id"`           // 座席ID
	CustomerServiceId uint64 `json:"customer_service_id" orm:"customer_service_id"` // 客服ID
	Satisfaction int        `json:"satisfaction" orm:"satisfaction"` // 满意度评分：1-5分
	CreatedAt   *gtime.Time `json:"created_at" orm:"created_at"`     // 创建时间
	UpdatedAt   *gtime.Time `json:"updated_at" orm:"updated_at"`     // 更新时间
}

// VisitorBrowseRecord 访客浏览记录表
type VisitorBrowseRecord struct {
	Id          uint64      `json:"id" orm:"id,primary"`             // ID
	SiteId      uint64      `json:"site_id" orm:"site_id"`           // 站点ID
	VisitorId   string      `json:"visitor_id" orm:"visitor_id"`     // 访客唯一标识
	SessionId   string      `json:"session_id" orm:"session_id"`     // 会话ID
	PageUrl     string      `json:"page_url" orm:"page_url"`         // 页面URL
	PageTitle   string      `json:"page_title" orm:"page_title"`     // 页面标题
	Referrer    string      `json:"referrer" orm:"referrer"`         // 来源页面
	StayTime    int         `json:"stay_time" orm:"stay_time"`       // 停留时间(秒)
	VisitTime   *gtime.Time `json:"visit_time" orm:"visit_time"`     // 访问时间
	CreatedAt   *gtime.Time `json:"created_at" orm:"created_at"`     // 创建时间
}
