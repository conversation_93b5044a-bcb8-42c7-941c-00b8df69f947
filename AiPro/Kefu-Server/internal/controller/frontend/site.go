package frontend

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SiteController struct{}

func NewSiteController() *SiteController {
	return &SiteController{}
}

// GetSiteConfig 获取站点配置信息
// GET /api/frontend/site/config?site_key=xxx
func (c *SiteController) GetSiteConfig(r *ghttp.Request) {
	siteKey := r.Get("site_key").String()

	if siteKey == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "站点密钥不能为空",
			"data": nil,
		})
	}

	// 暂时返回模拟数据，用于测试API接口
	mockConfig := map[string]interface{}{
		"site": map[string]interface{}{
			"id":       1,
			"name":     "测试站点",
			"domain":   "example.com",
			"site_key": siteKey,
			"status":   1,
		},
		"site_settings": map[string]interface{}{
			"welcome_message":    "欢迎访问我们的网站！",
			"offline_message":    "客服暂时离线，请留言",
			"auto_reply_enabled": 1,
			"auto_reply_message": "您好，我是智能客服，请问有什么可以帮助您的？",
		},
		"invite_popup_settings": map[string]interface{}{
			"enabled":        1,
			"title":          "需要帮助吗？",
			"content":        "我们的客服团队随时为您服务",
			"show_delay":     3,
			"show_frequency": 1,
			"position":       "center",
		},
		"consult_icon_settings": map[string]interface{}{
			"enabled":   1,
			"icon_type": "default",
			"position":  "bottom-right",
			"offset_x":  20,
			"offset_y":  20,
			"size":      "medium",
		},
		"online_customer_services": []map[string]interface{}{
			{
				"id":       1,
				"nickname": "客服小王",
				"avatar":   "/avatars/service1.jpg",
				"status":   1,
			},
			{
				"id":       2,
				"nickname": "客服小李",
				"avatar":   "/avatars/service2.jpg",
				"status":   1,
			},
		},
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": mockConfig,
	})
}
