package backend

import (
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type CompanyController struct{}

func NewCompanyController() *CompanyController {
	return &CompanyController{}
}

// CreateCompany 创建公司
// POST /api/backend/company/create
func (c *CompanyController) CreateCompany(r *ghttp.Request) {
	ctx := r.Context()

	var req CreateCompanyReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 创建公司
	company, err := service.Company().CreateCompany(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建公司失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "创建公司失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": company,
	})
}

// UpdateCompany 更新公司
// PUT /api/backend/company/update
func (c *CompanyController) UpdateCompany(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateCompanyReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新公司
	err := service.Company().UpdateCompany(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新公司失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新公司失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// GetCompanyList 获取公司列表
// GET /api/backend/company/list?page=1&limit=10&keyword=xxx
func (c *CompanyController) GetCompanyList(r *ghttp.Request) {
	ctx := r.Context()

	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 10).Int()
	keyword := r.Get("keyword").String()

	// 获取公司列表
	list, total, err := service.Company().GetCompanyList(ctx, page, limit, keyword)
	if err != nil {
		g.Log().Error(ctx, "获取公司列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取公司列表失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// DeleteCompany 删除公司
// DELETE /api/backend/company/delete/:id
func (c *CompanyController) DeleteCompany(r *ghttp.Request) {
	ctx := r.Context()

	id := r.Get("id").Uint64()
	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "公司ID不能为空",
			"data": nil,
		})
	}

	// 删除公司
	err := service.Company().DeleteCompany(ctx, id)
	if err != nil {
		g.Log().Error(ctx, "删除公司失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "删除公司失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// CreateCompanyReq 创建公司请求
type CreateCompanyReq struct {
	Name         string `json:"name" v:"required#公司名称不能为空"`
	ContactName  string `json:"contact_name" v:"required#联系人姓名不能为空"`
	ContactPhone string `json:"contact_phone" v:"required#联系人电话不能为空"`
	ContactEmail string `json:"contact_email" v:"required|email#联系人邮箱不能为空|邮箱格式不正确"`
	ExpireTime   string `json:"expire_time"`
}

// UpdateCompanyReq 更新公司请求
type UpdateCompanyReq struct {
	Id           uint64 `json:"id" v:"required#公司ID不能为空"`
	Name         string `json:"name" v:"required#公司名称不能为空"`
	ContactName  string `json:"contact_name" v:"required#联系人姓名不能为空"`
	ContactPhone string `json:"contact_phone" v:"required#联系人电话不能为空"`
	ContactEmail string `json:"contact_email" v:"required|email#联系人邮箱不能为空|邮箱格式不正确"`
	Status       int    `json:"status" v:"required|in:0,1#状态不能为空|状态值无效"`
	ExpireTime   string `json:"expire_time"`
}
