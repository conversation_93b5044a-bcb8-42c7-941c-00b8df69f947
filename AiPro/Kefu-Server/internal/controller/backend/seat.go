package backend

import (
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SeatController struct{}

func NewSeatController() *SeatController {
	return &SeatController{}
}

// CreateSeat 创建座席
// POST /api/backend/seat/create
func (c *SeatController) CreateSeat(r *ghttp.Request) {
	ctx := r.Context()

	var req CreateSeatReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 创建座席
	seat, err := service.Seat().CreateSeat(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建座席失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "创建座席失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": seat,
	})
}

// RenewSeat 续费座席
// POST /api/backend/seat/renew
func (c *SeatController) RenewSeat(r *ghttp.Request) {
	ctx := r.Context()

	var req RenewSeatReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 续费座席
	err := service.Seat().RenewSeat(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "续费座席失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "续费座席失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "续费成功",
		"data": nil,
	})
}

// GetSeatList 获取座席列表
// GET /api/backend/seat/list?company_id=1&page=1&limit=10
func (c *SeatController) GetSeatList(r *ghttp.Request) {
	ctx := r.Context()

	companyId := r.Get("company_id").Uint64()
	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 10).Int()

	if companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "公司ID不能为空",
			"data": nil,
		})
	}

	// 获取座席列表
	list, total, err := service.Seat().GetSeatList(ctx, companyId, page, limit)
	if err != nil {
		g.Log().Error(ctx, "获取座席列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取座席列表失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// AllocateSeatToSite 为站点分配座席
// POST /api/backend/seat/allocate
func (c *SeatController) AllocateSeatToSite(r *ghttp.Request) {
	ctx := r.Context()

	var req AllocateSeatReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 分配座席
	err := service.Seat().AllocateSeatToSite(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "分配座席失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "分配座席失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "分配成功",
		"data": nil,
	})
}

// CreateSeatReq 创建座席请求
type CreateSeatReq struct {
	CompanyId   uint64 `json:"company_id" v:"required#公司ID不能为空"`
	Name        string `json:"name" v:"required#座席名称不能为空"`
	MaxDuration int    `json:"max_duration" v:"required|min:1#最大时长不能为空|最大时长必须大于0"`
	ExpireTime  string `json:"expire_time"`
}

// RenewSeatReq 续费座席请求
type RenewSeatReq struct {
	SeatId           uint64 `json:"seat_id" v:"required#座席ID不能为空"`
	AddDuration      int    `json:"add_duration" v:"required|min:1#续费时长不能为空|续费时长必须大于0"`
	ExtendExpireTime string `json:"extend_expire_time"`
}

// AllocateSeatReq 分配座席请求
type AllocateSeatReq struct {
	SiteId   uint64 `json:"site_id" v:"required#站点ID不能为空"`
	SeatId   uint64 `json:"seat_id" v:"required#座席ID不能为空"`
	Priority int    `json:"priority" v:"required|min:1#优先级不能为空|优先级必须大于0"`
}
