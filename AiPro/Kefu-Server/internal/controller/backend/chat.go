package backend

import (
	"context"
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type ChatController struct{}

func NewChatController() *ChatController {
	return &ChatController{}
}

// GetOnlineVisitors 获取在线访客列表
// GET /api/backend/chat/online-visitors?site_id=1&page=1&limit=10
func (c *ChatController) GetOnlineVisitors(r *ghttp.Request) {
	ctx := r.Context()
	
	siteId := r.Get("site_id").Uint64()
	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 10).Int()

	if siteId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "站点ID不能为空",
			"data": nil,
		})
	}

	// 获取在线访客列表
	list, total, err := service.Chat().GetOnlineVisitors(ctx, siteId, page, limit)
	if err != nil {
		g.Log().Error(ctx, "获取在线访客列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取在线访客列表失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// GetChatSessions 获取聊天会话列表
// GET /api/backend/chat/sessions?customer_service_id=1&page=1&limit=10
func (c *ChatController) GetChatSessions(r *ghttp.Request) {
	ctx := r.Context()
	
	customerServiceId := r.Get("customer_service_id").Uint64()
	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 10).Int()

	if customerServiceId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "客服ID不能为空",
			"data": nil,
		})
	}

	// 获取聊天会话列表
	list, total, err := service.Chat().GetChatSessions(ctx, customerServiceId, page, limit)
	if err != nil {
		g.Log().Error(ctx, "获取聊天会话列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取聊天会话列表失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// SendMessage 客服发送消息
// POST /api/backend/chat/send
func (c *ChatController) SendMessage(r *ghttp.Request) {
	ctx := r.Context()
	
	var req SendMessageReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 发送消息
	message, err := service.Chat().SendCustomerServiceMessage(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "发送消息失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "发送消息失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "发送成功",
		"data": message,
	})
}

// TransferChat 转接聊天
// POST /api/backend/chat/transfer
func (c *ChatController) TransferChat(r *ghttp.Request) {
	ctx := r.Context()
	
	var req TransferChatReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 转接聊天
	err := service.Chat().TransferChat(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "转接聊天失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "转接聊天失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "转接成功",
		"data": nil,
	})
}

// EndChat 结束聊天
// POST /api/backend/chat/end
func (c *ChatController) EndChat(r *ghttp.Request) {
	ctx := r.Context()
	
	var req EndChatReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 结束聊天
	err := service.Chat().EndChat(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "结束聊天失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "结束聊天失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "结束成功",
		"data": nil,
	})
}

// GetChatHistory 获取聊天历史
// GET /api/backend/chat/history?session_id=xxx&page=1&limit=20
func (c *ChatController) GetChatHistory(r *ghttp.Request) {
	ctx := r.Context()
	
	sessionId := r.Get("session_id").String()
	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 20).Int()

	if sessionId == "" {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "会话ID不能为空",
			"data": nil,
		})
	}

	// 获取聊天历史
	history, total, err := service.Chat().GetChatHistory(ctx, sessionId, page, limit)
	if err != nil {
		g.Log().Error(ctx, "获取聊天历史失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取聊天历史失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  history,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// SendMessageReq 发送消息请求
type SendMessageReq struct {
	SessionId         string `json:"session_id" v:"required#会话ID不能为空"`
	CustomerServiceId uint64 `json:"customer_service_id" v:"required#客服ID不能为空"`
	MessageType       int    `json:"message_type" v:"required|in:1,2,3#消息类型不能为空|消息类型无效"`
	Content           string `json:"content"`
	FileUrl           string `json:"file_url"`
	FileName          string `json:"file_name"`
	FileSize          int64  `json:"file_size"`
}

// TransferChatReq 转接聊天请求
type TransferChatReq struct {
	SessionId               string `json:"session_id" v:"required#会话ID不能为空"`
	FromCustomerServiceId   uint64 `json:"from_customer_service_id" v:"required#原客服ID不能为空"`
	ToCustomerServiceId     uint64 `json:"to_customer_service_id" v:"required#目标客服ID不能为空"`
	TransferReason          string `json:"transfer_reason"`
}

// EndChatReq 结束聊天请求
type EndChatReq struct {
	SessionId         string `json:"session_id" v:"required#会话ID不能为空"`
	CustomerServiceId uint64 `json:"customer_service_id" v:"required#客服ID不能为空"`
	EndReason         string `json:"end_reason"`
}
