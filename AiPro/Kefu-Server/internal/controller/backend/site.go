package backend

import (
	"kefu-server/internal/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SiteController struct{}

func NewSiteController() *SiteController {
	return &SiteController{}
}

// CreateSite 创建站点
// POST /api/backend/site/create
func (c *SiteController) CreateSite(r *ghttp.Request) {
	ctx := r.Context()

	var req CreateSiteReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 创建站点
	site, err := service.Site().CreateSite(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "创建站点失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "创建站点失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "创建成功",
		"data": site,
	})
}

// UpdateSite 更新站点
// PUT /api/backend/site/update
func (c *SiteController) UpdateSite(r *ghttp.Request) {
	ctx := r.Context()

	var req UpdateSiteReq
	if err := r.Parse(&req); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 更新站点
	err := service.Site().UpdateSite(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "更新站点失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "更新站点失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "更新成功",
		"data": nil,
	})
}

// GetSiteList 获取站点列表
// GET /api/backend/site/list?company_id=1&page=1&limit=10
func (c *SiteController) GetSiteList(r *ghttp.Request) {
	ctx := r.Context()

	companyId := r.Get("company_id").Uint64()
	page := r.Get("page", 1).Int()
	limit := r.Get("limit", 10).Int()

	if companyId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "公司ID不能为空",
			"data": nil,
		})
	}

	// 获取站点列表
	list, total, err := service.Site().GetSiteList(ctx, companyId, page, limit)
	if err != nil {
		g.Log().Error(ctx, "获取站点列表失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "获取站点列表失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "获取成功",
		"data": g.Map{
			"list":  list,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// DeleteSite 删除站点
// DELETE /api/backend/site/delete/:id
func (c *SiteController) DeleteSite(r *ghttp.Request) {
	ctx := r.Context()

	id := r.Get("id").Uint64()
	if id == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "站点ID不能为空",
			"data": nil,
		})
	}

	// 删除站点
	err := service.Site().DeleteSite(ctx, id)
	if err != nil {
		g.Log().Error(ctx, "删除站点失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  "删除站点失败",
			"data": nil,
		})
	}

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// CreateSiteReq 创建站点请求
type CreateSiteReq struct {
	CompanyId uint64 `json:"company_id" v:"required#公司ID不能为空"`
	Name      string `json:"name" v:"required#站点名称不能为空"`
	Domain    string `json:"domain" v:"required#站点域名不能为空"`
}

// UpdateSiteReq 更新站点请求
type UpdateSiteReq struct {
	Id     uint64 `json:"id" v:"required#站点ID不能为空"`
	Name   string `json:"name" v:"required#站点名称不能为空"`
	Domain string `json:"domain" v:"required#站点域名不能为空"`
	Status int    `json:"status" v:"required|in:0,1#状态不能为空|状态值无效"`
}
