package dao

import (
	"kefu-server/internal/dao/internal"
)

// 数据访问对象实例
var (
	Company                   = internal.NewCompanyDao()
	Seat                      = internal.NewSeatDao()
	Site                      = internal.NewSiteDao()
	SiteSettings              = internal.NewSiteSettingsDao()
	InvitePopupSettings       = internal.NewInvitePopupSettingsDao()
	ConsultIconSettings       = internal.NewConsultIconSettingsDao()
	QuickChatSettings         = internal.NewQuickChatSettingsDao()
	IndependentChatSettings   = internal.NewIndependentChatSettingsDao()
	VisitorMessageSettings    = internal.NewVisitorMessageSettingsDao()
	Visitor                   = internal.NewVisitorDao()
	OnlineVisitor             = internal.NewOnlineVisitorDao()
	VisitorHistory            = internal.NewVisitorHistoryDao()
	VisitorBrowseRecord       = internal.NewVisitorBrowseRecordDao()
	ChatMessage               = internal.NewChatMessageDao()
	SkillGroup                = internal.NewSkillGroupDao()
	CustomerService           = internal.NewCustomerServiceDao()
	SeatAllocation            = internal.NewSeatAllocationDao()
	SkillGroupCustomerService = internal.NewSkillGroupCustomerServiceDao()
)
