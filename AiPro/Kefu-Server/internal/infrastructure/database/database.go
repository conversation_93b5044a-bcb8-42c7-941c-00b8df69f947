package database

import (
	"context"
	"kefu-server/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

var DB gdb.DB

func Init(ctx context.Context) {
	DB = g.DB()
	
	// 自动迁移数据库表
	if err := autoMigrate(ctx); err != nil {
		g.Log().Fatal(ctx, "数据库迁移失败:", err)
	}
	
	g.Log().Info(ctx, "数据库初始化完成")
}

func autoMigrate(ctx context.Context) error {
	// 创建所有表
	tables := []interface{}{
		&entity.Company{},
		&entity.Seat{},
		&entity.Site{},
		&entity.SiteSettings{},
		&entity.InvitePopupSettings{},
		&entity.ConsultIconSettings{},
		&entity.QuickChatSettings{},
		&entity.IndependentChatSettings{},
		&entity.VisitorMessageSettings{},
		&entity.Visitor{},
		&entity.OnlineVisitor{},
		&entity.VisitorHistory{},
		&entity.VisitorBrowseRecord{},
		&entity.ChatMessage{},
		&entity.SkillGroup{},
		&entity.CustomerService{},
		&entity.SeatAllocation{},
		&entity.SkillGroupCustomerService{},
	}

	for _, table := range tables {
		if err := createTableIfNotExists(ctx, table); err != nil {
			return err
		}
	}

	return nil
}

func createTableIfNotExists(ctx context.Context, model interface{}) error {
	// 这里使用GoFrame的数据库操作来创建表
	// 实际项目中可能需要使用更复杂的迁移工具
	glog.Info(ctx, "检查并创建表:", model)
	return nil
}
