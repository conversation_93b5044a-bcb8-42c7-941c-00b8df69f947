package service

import (
	"kefu-server/internal/service/company"
	"kefu-server/internal/service/seat"
	"kefu-server/internal/service/site"
	"kefu-server/internal/service/settings"
	"kefu-server/internal/service/chat"
)

// 服务实例
var (
	companyService  *company.Service
	seatService     *seat.Service
	siteService     *site.Service
	settingsService *settings.Service
	chatService     *chat.Service
)

func init() {
	companyService = company.New()
	seatService = seat.New()
	siteService = site.New()
	settingsService = settings.New()
	chatService = chat.New()
}

// Company 公司服务
func Company() *company.Service {
	return companyService
}

// Seat 座席服务
func Seat() *seat.Service {
	return seatService
}

// Site 站点服务
func Site() *site.Service {
	return siteService
}

// Settings 设置服务
func Settings() *settings.Service {
	return settingsService
}

// Chat 聊天服务
func Chat() *chat.Service {
	return chatService
}
