package seat

import (
	"context"
	"kefu-server/internal/model/entity"
)

type Service struct{}

func New() *Service {
	return &Service{}
}

// CreateSeat 创建座席
func (s *Service) CreateSeat(ctx context.Context, req interface{}) (*entity.Seat, error) {
	// TODO: 实现创建座席逻辑
	return nil, nil
}

// RenewSeat 续费座席
func (s *Service) RenewSeat(ctx context.Context, req interface{}) error {
	// TODO: 实现续费座席逻辑
	return nil
}

// GetSeatList 获取座席列表
func (s *Service) GetSeatList(ctx context.Context, companyId uint64, page, limit int) ([]entity.Seat, int, error) {
	// TODO: 实现获取座席列表逻辑
	return nil, 0, nil
}

// AllocateSeatToSite 为站点分配座席
func (s *Service) AllocateSeatToSite(ctx context.Context, req interface{}) error {
	// TODO: 实现分配座席逻辑
	return nil
}
